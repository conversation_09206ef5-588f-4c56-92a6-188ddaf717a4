<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Share extends Model
{
    protected $fillable = [
        'user_id',
        'post_id',
        'message',
        'share_type',
        'privacy_scope',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who shared the post
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the post that was shared
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }

    /**
     * Get all comments for this share
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get all likes for this share
     */
    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    /**
     * Check if the share is liked by a specific user
     */
    public function isLikedBy(User $user): bool
    {
        return $this->likes()->where('user_id', $user->id)->exists();
    }

    /**
     * Get available privacy scopes
     */
    public static function getPrivacyScopes(): array
    {
        return [
            'public' => [
                'label' => 'Public',
                'description' => 'Anyone can see this',
                'icon' => 'globe'
            ],
            'friends' => [
                'label' => 'Friends',
                'description' => 'Your friends can see this',
                'icon' => 'users'
            ],
            'only_me' => [
                'label' => 'Only me',
                'description' => 'Only you can see this',
                'icon' => 'lock'
            ],
            'custom' => [
                'label' => 'Custom',
                'description' => 'Custom privacy settings',
                'icon' => 'settings'
            ]
        ];
    }

    /**
     * Get privacy scope details
     */
    public function getPrivacyScopeDetails(): array
    {
        $scopes = self::getPrivacyScopes();
        return $scopes[$this->privacy_scope] ?? $scopes['public'];
    }

    /**
     * Check if share is visible to a specific user
     */
    public function isVisibleTo(?User $user): bool
    {
        switch ($this->privacy_scope) {
            case 'public':
                return true;

            case 'friends':
                // For now, we'll consider all users as "friends"
                // In a real app, you'd check friendship relationships
                return $user !== null;

            case 'only_me':
                return $user && $user->id === $this->user_id;

            case 'custom':
                // For now, treat custom as friends
                // In a real app, you'd implement custom privacy logic
                return $user !== null;

            default:
                return true;
        }
    }

    /**
     * Scope to filter shares visible to a specific user
     */
    public function scopeVisibleTo($query, ?User $user)
    {
        if (!$user) {
            // Only public shares for guests
            return $query->where('privacy_scope', 'public');
        }

        return $query->where(function($q) use ($user) {
            $q->where('privacy_scope', 'public')
              ->orWhere(function($subQ) use ($user) {
                  $subQ->where('privacy_scope', 'friends');
                  // In a real app, you'd add friendship checks here
              })
              ->orWhere(function($subQ) use ($user) {
                  $subQ->where('privacy_scope', 'only_me')
                       ->where('user_id', $user->id);
              })
              ->orWhere(function($subQ) use ($user) {
                  $subQ->where('privacy_scope', 'custom');
                  // In a real app, you'd add custom privacy logic here
              });
        });
    }
}
