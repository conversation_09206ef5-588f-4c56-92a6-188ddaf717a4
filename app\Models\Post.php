<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Post extends Model
{
    protected $fillable = [
        'title',
        'content',
        'type',
        'images',
        'facebook_embed_url',
        'is_pinned',
        'status',
        'approval_status',
        'approved_at',
        'approved_by',
        'user_id',
        'organization_id',
        'group_id',
        'published_at',
    ];

    protected $casts = [
        'images' => 'array',
        'is_pinned' => 'boolean',
        'published_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the user who created this post
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization this post belongs to
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the group this post belongs to
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    /**
     * Get the user who approved this post
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get all file attachments for this post
     */
    public function fileAttachments(): HasMany
    {
        return $this->hasMany(Attachment::class);
    }

    /**
     * Get all comments for this post
     */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get all likes for this post
     */
    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    /**
     * Get all shares for this post
     */
    public function shares(): HasMany
    {
        return $this->hasMany(Share::class);
    }

    /**
     * Check if the post is liked by a specific user
     */
    public function isLikedBy(User $user): bool
    {
        return $this->likes()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if the post is shared by a specific user
     */
    public function isSharedBy(User $user): bool
    {
        return $this->shares()->where('user_id', $user->id)->exists();
    }

    /**
     * Scope for published posts
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope for pinned posts
     */
    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    /**
     * Scope for approved posts
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope for pending approval posts
     */
    public function scopePendingApproval($query)
    {
        return $query->where('approval_status', 'pending');
    }

    /**
     * Scope for organization posts
     */
    public function scopeOrganizationPosts($query)
    {
        return $query->whereNotNull('organization_id')->whereNull('group_id');
    }

    /**
     * Scope for group posts
     */
    public function scopeGroupPosts($query)
    {
        return $query->whereNotNull('group_id');
    }

    /**
     * Scope for personal posts (not in org or group)
     */
    public function scopePersonalPosts($query)
    {
        return $query->whereNull('organization_id')->whereNull('group_id');
    }

    /**
     * Check if post needs approval
     */
    public function needsApproval(): bool
    {
        return $this->approval_status === 'pending';
    }

    /**
     * Check if post is approved
     */
    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    /**
     * Check if post is rejected
     */
    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    /**
     * Get the context (organization or group) this post belongs to
     */
    public function getContext()
    {
        if ($this->group_id) {
            return $this->group;
        }

        if ($this->organization_id) {
            return $this->organization;
        }

        return null;
    }

    /**
     * Get the context type
     */
    public function getContextType(): ?string
    {
        if ($this->group_id) {
            return 'group';
        }

        if ($this->organization_id) {
            return 'organization';
        }

        return 'personal';
    }
}
