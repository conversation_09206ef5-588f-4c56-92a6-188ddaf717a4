@props(['post', 'showInline' => false])

<div class="comment-section" data-post-id="{{ $post->id }}">
    @if(!$showInline)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    Comments (<span id="comments-count-{{ $post->id }}">{{ $post->comments->count() }}</span>)
                </h3>
    @endif
                
    <!-- Add Comment Form -->
    @auth
        <form class="comment-form mb-6" data-post-id="{{ $post->id }}" data-parent-id="">
            @csrf
            <div class="flex space-x-3">
                <img class="h-10 w-10 rounded-full flex-shrink-0"
                     src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                     alt="{{ auth()->user()->name }}">
                <div class="flex-1">
                    <textarea name="content" rows="3"
                              placeholder="Write a comment..."
                              class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none"
                              required></textarea>
                    <div class="mt-2 flex justify-end">
                        <button type="submit"
                                class="px-4 py-2 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                            Post Comment
                        </button>
                    </div>
                </div>
            </div>
        </form>
    @else
        <div class="mb-6 p-4 bg-gray-50 rounded-lg text-center">
            <p class="text-gray-600">
                <a href="{{ route('login') }}" class="text-custom-green hover:text-custom-second-darkest font-medium">Sign in</a>
                to join the conversation
            </p>
        </div>
    @endauth

    <!-- Comments List -->
    <div class="comments-list space-y-4" id="comments-list-{{ $post->id }}">
        @forelse($post->comments->whereNull('parent_id') as $comment)
            <x-comment-item :comment="$comment" :post="$post" />
        @empty
            <div class="no-comments text-gray-500 text-center py-8">
                <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p>No comments yet. Be the first to comment!</p>
            </div>
        @endforelse
    </div>

    @if(!$showInline)
            </div>
        </div>
    @endif
</div>

<style>
.comment-section .comment-form textarea:focus {
    min-height: 80px;
    transition: min-height 0.2s ease;
}

.comment-section .reply-form {
    margin-top: 12px;
    padding-left: 40px;
}

.comment-section .comment-item {
    transition: background-color 0.2s ease;
}

.comment-section .comment-item:hover {
    background-color: rgba(0, 0, 0, 0.01);
}

.comment-section .comment-actions button {
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.comment-section .comment-actions button:hover {
    transform: translateY(-1px);
}

.comment-section .nested-comments {
    border-left: 2px solid #e5e7eb;
    margin-left: 20px;
    padding-left: 20px;
}

.comment-section .comment-edit-form textarea {
    font-size: 0.875rem;
}

/* Facebook-style comment bubbles */
.comment-section .comment-item .bg-gray-50 {
    background-color: #f0f2f5;
    border-radius: 16px;
}

/* Smooth animations */
.comment-section .hidden {
    display: none;
}

.comment-section .comment-actions button:active {
    transform: translateY(0);
}
</style>

<style>
.comment-section .comment-form textarea:focus {
    min-height: 80px;
    transition: min-height 0.2s ease;
}

.comment-section .reply-form {
    margin-top: 12px;
    padding-left: 40px;
}

.comment-section .comment-item {
    transition: background-color 0.2s ease;
}

.comment-section .comment-item:hover {
    background-color: rgba(0, 0, 0, 0.01);
}

.comment-section .comment-actions button {
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.comment-section .comment-actions button:hover {
    transform: translateY(-1px);
}

.comment-section .nested-comments {
    border-left: 2px solid #e5e7eb;
    margin-left: 20px;
    padding-left: 20px;
}

.comment-section .comment-edit-form textarea {
    font-size: 0.875rem;
}

/* Facebook-style comment bubbles */
.comment-section .comment-item .bg-gray-50 {
    background-color: #f0f2f5;
    border-radius: 16px;
}

/* Smooth animations */
.comment-section .hidden {
    display: none;
}

.comment-section .comment-actions button:active {
    transform: translateY(0);
}
</style>
